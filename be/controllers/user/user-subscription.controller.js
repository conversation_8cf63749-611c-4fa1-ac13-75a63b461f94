const paystack = require('paystack')(process.env.PAYSTACK_SECRET_KEY)
const UserSubscription = require('../../models/user/user-subscription')
const User = require('../../models/user/user')
const SubscriptionPackage = require('../../models/subscription/package')
const ErrorResponse = require('../../utils/errorResponse')
const asyncHandler = require('../../middleware/async')
const axios = require('axios')
const sendEmail = require('../../utils/sendEmail')
const { io, Socket } = require('../../utils/socket')

// Helper function to handle Paystack subscription plan changes
// Paystack doesn't have a direct update subscription endpoint
// We need to cancel the current subscription and create a new one
const handlePaystackSubscriptionUpdate = async (subscriptionCode, newPlanCode, customerEmail) => {
  try {
    console.log('Updating Paystack subscription:', { subscriptionCode, newPlanCode, customerEmail })

    // Paystack doesn't support direct subscription plan updates
    // We need to cancel the current subscription and create a new one
    console.log('Using subscription replacement method...')

    // Step 1: Disable the current subscription
    const disableResponse = await axios.post(
      'https://api.paystack.co/subscription/disable',
      {
        code: subscriptionCode,
        token: subscriptionCode,
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      },
    )

    console.log('Paystack disable response:', disableResponse.data)

    if (!disableResponse.data.status) {
      return {
        success: false,
        data: null,
        error: disableResponse.data.message || 'Failed to disable current subscription',
        method: 'replacement_failed',
      }
    }

    // Step 2: Create a new subscription with the new plan
    const createResponse = await axios.post(
      'https://api.paystack.co/subscription',
      {
        customer: customerEmail,
        plan: newPlanCode,
        start_date: new Date().toISOString(),
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      },
    )

    console.log('Paystack create response:', createResponse.data)

    return {
      success: createResponse.data.status,
      data: createResponse.data,
      error: createResponse.data.status ? null : createResponse.data.message,
      newSubscriptionCode: createResponse.data.data?.subscription_code,
      method: 'replacement',
      message: 'Subscription plan updated successfully',
    }

  } catch (error) {
    console.error('Paystack subscription update error:', error.response?.data || error.message)

    return {
      success: false,
      data: null,
      error: error.response?.data?.message || error.message || 'Paystack API error',
      method: 'error',
    }
  }
}

// Helper function to handle Paystack subscription cancellation
const handlePaystackSubscriptionCancellation = async subscriptionCode => {
  try {
    const response = await axios.post(
      'https://api.paystack.co/subscription/disable',
      {
        code: subscriptionCode,
        token: subscriptionCode,
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      },
    )

    return {
      success: response.data.status,
      data: response.data,
      error: response.data.status ? null : response.data.message,
    }
  } catch (error) {
    console.error('Paystack subscription cancellation error:', error.response?.data || error.message)

    return {
      success: false,
      data: null,
      error: error.response?.data?.message || error.message || 'Paystack API error',
    }
  }
}

// TODO: Fix after payment save data that user has paid
exports.createPaystackSubscription = asyncHandler(async (req, res, next) => {
  console.log('createSubscription', req)

  const { userId, email, paystackPlan, planType, paystackCustomerCode } = req

  try {
    const existing = await UserSubscription.findOne({ user: userId })

    // Trial logic
    if (!existing || !existing.hasUsedTrial) {
      const trialEndsAt = new Date()

      trialEndsAt.setDate(trialEndsAt.getDate() + 14)

      const sub = new UserSubscription({
        user: userId,
        planCode: null,
        paystackCustomerCode: null,
        status: 'trial',
        hasUsedTrial: true,
        trialEndsAt,
      })

      await sub.save()

      Socket.emit('trialStarted', {
        message: 'Trial started',
        endsAt: trialEndsAt,
      })

      // return // Exit after trial setup
    }

    // Paid plan logic
    const plans = {
      basicMonthly: 'PLN_lt6ibafwu0fa26l',
      basicYearly: 'PLN_mmxnqegms4umb1w',
      allInMonthly: 'PLN_79dz0av80r8a56v',
      allInYearly: 'PLN_ea7er3kx4tvfbz2',
      productionMonthly: 'PLN_x01uu0iabedar51',
      productionYearly: 'PLN_kdvlus5vynenupv',
      noLimitsMonthly: 'PLN_5u5gl75qhetxa4u',
      noLimitsYearly: 'PLN_4rp5wgoa8h79n4q',
    }

    console.log('paystackPlan', paystackPlan)

    const selectedPlanKey = Object.keys(plans).find(
      key => plans[key] === paystackPlan,
    )

    console.log('selectedPlan', selectedPlanKey)

    if (!selectedPlanKey) {
      Socket.emit('subscriptionError', { message: 'Invalid plan type' })

      return
    }

    const selectedPlanCode = plans[selectedPlanKey]

    // TODO: fix callback

    // Initialize Paystack transaction
    const transaction = await paystack.transaction.initialize({
      email,
      amount: req.price,
      callback_url: `https://app.qwotez.com/subscription/callback?planType=${selectedPlanKey}`,
      plan: selectedPlanCode, // ✅ Use actual Paystack plan code here
    })

    console.log('Paystack transaction initialized:', transaction.data)

    Socket.emit('redirectToPaystack', {
      url: transaction.data.authorization_url,
      reference: transaction.data.reference,
    })

  } catch (err) {
    console.error('Subscription error:', err)
    Socket.emit('subscriptionError', { message: 'Failed to process subscription' })
  }
})

exports.upgradePaystackSubscription = asyncHandler(async (req, res) => {
  console.log('upgradePaystackSubscription', req)

  const { userId, newPlanCode, email, price } = req

  const subscription = await UserSubscription.findOne({ user: userId })

  if (subscription && subscription.paystackSubscriptionCode) {
    try {
      // Step 1: Cancel current subscription
      await paystack.subscription.disable({
        code: subscription.paystackSubscriptionCode,
        token: subscription.paystackEmailToken,
      })

      subscription.status = 'cancelled'
      await subscription.save()
    } catch (error) {
      io.to(req.user).emit('upgradeUserSubscription', {
        message: 'Failed to cancel existing subscription',
      })
    }
  }

  // Step 2: Start a new subscription (reusing your existing logic)
  try {
    const transaction = await paystack.transaction.initialize({
      email,
      amount: price,
      plan: newPlanCode,
      callback_url: 'https://app.qwotez.com/subscription/callback?upgrade=true',
    })

    io.to(req.user).emit('upgradeUserSubscription', {
      url: transaction.data.authorization_url,
      reference: transaction.data.reference,
    })
  } catch (error) {
    console.error('Upgrade error:', error)
    io.to(req.user).emit('upgradeUserSubscription', {
      message: 'Failed to initiate upgrade transaction',
    })
  }
})

exports.cancelPaystackSubscription = asyncHandler(async (req, res) => {
  const { userId } = req

  // Get user's current subscription from DB
  const subscription = await UserSubscription.findOne({ user: userId })

  if (!subscription || !subscription.paystackSubscriptionCode) {
    io.to(req.user).emit('cancelUserSubscription', {
      message: 'No active subscription found.',
    })
  }

  try {
    const response = await paystack.subscription.disable({
      code: subscription.paystackSubscriptionCode,
      token: subscription.paystackEmailToken, // This is required to disable the subscription
    })

    if (response.status) {
      // Update DB
      subscription.status = 'cancelled'
      await subscription.save()

      io.to(req.user).emit('cancelUserSubscription', {
        message: 'Subscription cancelled successfully.',
      })
    } else {
      throw new Error(response.message)
    }
  } catch (error) {
    console.error('Cancel error:', error)

    io.to(req.user).emit('cancelUserSubscription', {
      message: 'Create new scratch file from selection',
    })
  }
})

// @desc    Upgrade from trial to paid subscription
// @route   Socket: paystackController:upgradeFromTrial
// @access  Private
exports.upgradeFromTrial = asyncHandler(async (req, res, next) => {
  console.log('upgradeFromTrial', req)

  const { userId, email, paystackPlan, planType, price, planId } = req

  try {
    // Find existing subscription
    const userSubscription = await UserSubscription.findOne({ user: userId })

    if (!userSubscription) {
      io.to(req.user).emit('subscriptionError', {
        status: 'error',
        message: 'No subscription found',
      })

      return
    }

    if (userSubscription.status !== 'trial' && userSubscription.status !== 'expired') {
      io.to(req.user).emit('subscriptionError', {
        status: 'error',
        message: 'User is not on trial',
      })

      return
    }

    // Get plan codes mapping
    const plans = {
      basicMonthly: 'PLN_lt6ibafwu0fa26l',
      basicYearly: 'PLN_mmxnqegms4umb1w',
      allInMonthly: 'PLN_79dz0av80r8a56v',
      allInYearly: 'PLN_ea7er3kx4tvfbz2',
      productionMonthly: 'PLN_x01uu0iabedar51',
      productionYearly: 'PLN_kdvlus5vynenupv',
      noLimitsMonthly: 'PLN_5u5gl75qhetxa4u',
      noLimitsYearly: 'PLN_4rp5wgoa8h79n4q',
    }

    const selectedPlanKey = Object.keys(plans).find(
      key => plans[key] === paystackPlan,
    )

    if (!selectedPlanKey) {
      io.to(req.user).emit('subscriptionError', {
        status: 'error',
        message: 'Invalid plan type',
      })

      return
    }

    const selectedPlanCode = plans[selectedPlanKey]

    // Initialize Paystack transaction for upgrade
    const transaction = await paystack.transaction.initialize({
      email,
      amount: price * 100, // Convert to kobo
      callback_url: `https://app.qwotez.com/subscription/callback?planType=${selectedPlanKey}&userId=${userId}&upgrade=true`,
      plan: selectedPlanCode,
      metadata: {
        userId,
        planId,
        upgradeFromTrial: true,
      },
    })

    console.log('Paystack transaction initialized for trial upgrade:', transaction.data)

    // Emit to specific user's room only
    io.to(req.user).emit('redirectToPaystack', {
      status: 'success',
      message: 'Redirecting to payment...',
      data: {
        url: transaction.data.authorization_url,
        reference: transaction.data.reference,
        upgradeFromTrial: true,
      },
    })

  } catch (err) {
    console.error('Trial upgrade error:', err)
    io.to(req.user).emit('subscriptionError', {
      status: 'error',
      message: 'Failed to process trial upgrade',
    })
  }
})

exports.subscriptionCallback = asyncHandler(async (req, res, next) => {
  const { reference, planType, userId, upgrade } = req.query

  try {
    // 1. Verify transaction with Paystack
    const verification = await axios.get(`https://api.paystack.co/transaction/verify/${ reference }`, {
      headers: {
        Authorization: `Bearer ${ process.env.PAYSTACK_SECRET_KEY }`,
      },
    })

    const data = verification.data.data

    if (data.status !== 'success') {
      return res.status(400).send('Payment verification failed')
    }

    const email = data.customer.email
    const isUpgradeFromTrial = upgrade === 'true' || data.metadata?.upgradeFromTrial

    // 2. Find user and update subscription
    const user = await User.findOne({ email })
    if (!user) {
      return res.status(404).send('User not found')
    }

    // Find user subscription
    let userSubscription = await UserSubscription.findOne({ user: user._id })

    if (isUpgradeFromTrial && userSubscription) {
      // Update existing trial subscription to paid
      userSubscription.status = 'active'
      userSubscription.planCode = getPlanCodeFromType(planType)
      userSubscription.paystackSubscriptionCode = data.authorization?.authorization_code || null
      userSubscription.paystackCustomerCode = data.customer?.customer_code || null
      userSubscription.startedAt = new Date()
      userSubscription.endsAt = new Date(Date.now() + (planType.includes('yearly') ? 365 : 30) * 24 * 60 * 60 * 1000)

      await userSubscription.save()

      console.log('Trial upgraded to paid subscription:', userSubscription)
    } else {
      // Create new subscription (fallback)
      userSubscription = new UserSubscription({
        user: user._id,
        status: 'active',
        planCode: getPlanCodeFromType(planType),
        paystackSubscriptionCode: data.authorization?.authorization_code || null,
        paystackCustomerCode: data.customer?.customer_code || null,
        startedAt: new Date(),
        endsAt: new Date(Date.now() + (planType.includes('yearly') ? 365 : 30) * 24 * 60 * 60 * 1000),
        hasUsedTrial: true,
      })

      await userSubscription.save()
    }

    // 3. Send confirmation email
    try {
      await sendEmail({
        email: user.email,
        subject: isUpgradeFromTrial ? 'Trial Upgraded - QwoteZ' : 'Subscription Confirmed - QwoteZ',
        message: `
          <h2>${isUpgradeFromTrial ? 'Trial Upgraded Successfully!' : 'Subscription Confirmed!'}</h2>
          <p>Thank you for ${isUpgradeFromTrial ? 'upgrading your trial to' : 'subscribing to'} the ${planType} plan.</p>
          <p>Your subscription is now active and will renew automatically.</p>
          <p>Welcome to QwoteZ!</p>
        `,
      })
    } catch (emailError) {
      console.error('Error sending confirmation email:', emailError)
    }

    // 4. Redirect to frontend success page
    const redirectUrl = isUpgradeFromTrial
      ? `https://app.qwotez.com/subscription/upgrade-success?plan=${planType}`
      : `https://app.qwotez.com/subscription/success?plan=${planType}`

    res.redirect(redirectUrl)

  } catch (error) {
    console.error('Callback error:', error.response?.data || error.message)
    res.status(500).send('Something went wrong')
  }
})

// Helper function to get plan code from plan type
function getPlanCodeFromType(planType) {
  const plans = {
    basicMonthly: 'PLN_lt6ibafwu0fa26l',
    basicYearly: 'PLN_mmxnqegms4umb1w',
    allInMonthly: 'PLN_79dz0av80r8a56v',
    allInYearly: 'PLN_ea7er3kx4tvfbz2',
    productionMonthly: 'PLN_x01uu0iabedar51',
    productionYearly: 'PLN_kdvlus5vynenupv',
    noLimitsMonthly: 'PLN_5u5gl75qhetxa4u',
    noLimitsYearly: 'PLN_4rp5wgoa8h79n4q',
  }

  return plans[planType] || null
}

// @desc    Update user subscription plan and maxIdentities
// @route   PUT /api/users/:id/subscription
// @access  Private (Admin or Self)
// @desc    Get user subscription details with billing information
// @route   GET /api/users/:id/subscription-details
// @access  Private (Self or Admin)
exports.getUserSubscriptionDetails = asyncHandler(async (req, res, next) => {
  console.log('getUserSubscriptionDetails', { req })
  try {
    // Check if current user is admin or requesting their own data
    const currentUser = await User.findById(req.user)
    const isAdmin = currentUser && currentUser.role === 'admin'
    const isOwnData = req.user.toString() === req.id.toString()

    console.log('Access check:', { isAdmin, isOwnData, currentUserId: req.user, targetUserId: req.id })

    if (!isAdmin && !isOwnData) {
      return io.to(req.user).emit('getUserSubscriptionDetails', {
        status: 'error',
        message: 'Access denied. You can only view your own subscription details or be an admin.',
      })
    }

    // Find the target user with subscription details
    const targetUser = await User.findById(req.id)
      .populate('subscriptionPlan', 'title description priceMonthly priceYearly maxIdentities features isPopular')
      .select('-password -resetPasswordToken -resetPasswordExpire')

    if (!targetUser) {
      return io.to(req.user).emit('getUserSubscriptionDetails', {
        status: 'error',
        message: 'User not found',
      })
    }

    // Find user subscription details
    let userSubscription = await UserSubscription.findOne({ user: req.id })
      .populate('currentPackage', 'title description priceMonthly priceYearly maxIdentities features isPopular')

    // If no subscription exists, create a trial subscription automatically
    if (!userSubscription) {
      // Double-check to prevent race conditions
      const existingSubscription = await UserSubscription.findOne({ user: req.id })
      if (!existingSubscription) {
        const trialEndsAt = new Date()

        trialEndsAt.setDate(trialEndsAt.getDate() + 14)

        userSubscription = new UserSubscription({
          user: req.id,
          planCode: null,
          paystackCustomerCode: null,
          status: 'trial',
          hasUsedTrial: true,
          trialEndsAt,
          startedAt: new Date(),
        })

        await userSubscription.save()
        console.log('Created new trial subscription for user:', req.id)
      } else {
        userSubscription = existingSubscription
        console.log('Found existing subscription during race condition check for user:', req.id)
      }
    }

    // Calculate actual subscription status based on expiration dates
    let actualStatus = 'trial'
    let daysRemaining = 0
    let totalDays = 0
    let nextBillingDate = null

    if (userSubscription) {
      const now = new Date()

      // Check if user has an active paid subscription
      if (userSubscription.endsAt && userSubscription.status === 'active') {
        if (new Date(userSubscription.endsAt) > now) {
          actualStatus = 'active'
          daysRemaining = Math.max(0, Math.ceil((new Date(userSubscription.endsAt) - now) / (1000 * 60 * 60 * 24)))
          totalDays = userSubscription.startedAt ?
            Math.ceil((new Date(userSubscription.endsAt) - new Date(userSubscription.startedAt)) / (1000 * 60 * 60 * 24)) : 30
          nextBillingDate = userSubscription.endsAt
        } else {
          // Paid subscription has expired
          actualStatus = 'expired'
          daysRemaining = 0
        }
      }

      // Check if user is on trial
      else if (userSubscription.trialEndsAt && userSubscription.status === 'trial') {
        if (new Date(userSubscription.trialEndsAt) > now) {
          actualStatus = 'trial'
          daysRemaining = Math.max(0, Math.ceil((new Date(userSubscription.trialEndsAt) - now) / (1000 * 60 * 60 * 24)))
          totalDays = 14
          nextBillingDate = userSubscription.trialEndsAt
        } else {
          // Trial has expired
          actualStatus = 'expired'
          daysRemaining = 0

          // Update the subscription status in the database
          userSubscription.status = 'expired'
          await userSubscription.save()
        }
      }

      // Check if subscription is already marked as expired
      else if (userSubscription.status === 'expired') {
        actualStatus = 'expired'
        daysRemaining = 0
      }
    }

    // Calculate subscription details
    const subscriptionDetails = {
      user: {
        id: targetUser._id,
        email: targetUser.email,
        username: targetUser.username,
        maxIdentities: targetUser.maxIdentities,
        maxIdentitiesOverride: targetUser.maxIdentitiesOverride,
        currentIdentityCount: targetUser.identities ? targetUser.identities.length : 0,
        remainingSlots: targetUser.getRemainingIdentitySlots(),
      },
      currentPlan: targetUser.subscriptionPlan || null,
      subscription: userSubscription ? {
        id: userSubscription._id,
        status: actualStatus, // Use calculated status instead of database status
        planCode: userSubscription.planCode,
        paystackSubscriptionCode: userSubscription.paystackSubscriptionCode,
        paystackCustomerCode: userSubscription.paystackCustomerCode,
        startedAt: userSubscription.startedAt,
        endsAt: userSubscription.endsAt,
        hasUsedTrial: userSubscription.hasUsedTrial,
        trialEndsAt: userSubscription.trialEndsAt,
        createdAt: userSubscription.createdAt,
        currentPackage: userSubscription.currentPackage,
        daysRemaining: daysRemaining,
        totalDays: totalDays,
      } : null,
      billing: {
        nextBillingDate: nextBillingDate,
        billingCycle: userSubscription?.planCode?.includes('yearly') ? 'yearly' : 'monthly',
        amount: targetUser.subscriptionPlan ?
          (userSubscription?.planCode?.includes('yearly') ?
            targetUser.subscriptionPlan.priceYearly :
            targetUser.subscriptionPlan.priceMonthly) : 0,
        currency: 'ZAR', // Updated to match Paystack currency from API test
        paymentMethod: userSubscription?.paystackCustomerCode ? 'Paystack' : null,
      },
    }

    io.to(req.user).emit('getUserSubscriptionDetails', {
      status: 'success',
      data: subscriptionDetails,
    })

  } catch (error) {
    console.error('Error getting user subscription details:', error)
    io.to(req.user).emit('getUserSubscriptionDetails', {
      status: 'error',
      message: error.message,
    })
  }
})

exports.updateUserSubscription = asyncHandler(async (req, res, next) => {
  try {
    const { subscriptionPlanId, billingCycle = 'monthly' } = req

    // Check if current user is admin or updating their own subscription
    const currentUser = await User.findById(req.user)
    const isAdmin = currentUser && currentUser.role === 'admin'
    const isOwnData = req.user.toString() === req.id.toString()

    if (!isAdmin && !isOwnData) {
      return io.to(req.user).emit('updateUserSubscription', {
        status: 'error',
        message: 'Access denied. You can only update your own subscription or be an admin.',
      })
    }

    // Find the target user
    const targetUser = await User.findById(req.id)
    if (!targetUser) {
      return io.to(req.user).emit('updateUserSubscription', {
        status: 'error',
        message: 'User not found',
      })
    }

    // Find the subscription package
    const subscriptionPackage = await SubscriptionPackage.findById(subscriptionPlanId)
    if (!subscriptionPackage) {
      return io.to(req.user).emit('updateUserSubscription', {
        status: 'error',
        message: 'Subscription package not found',
      })
    }

    // Find user subscription
    const userSubscription = await UserSubscription.findOne({ user: req.id })

    // Store previous values for comparison
    const previousSubscription = targetUser.subscriptionPlan
    const previousMaxIdentities = targetUser.maxIdentities
    const currentIdentityCount = targetUser.identities ? targetUser.identities.length : 0

    // Check if new plan allows enough identities for current usage
    if (!targetUser.maxIdentitiesOverride && subscriptionPackage.maxIdentities < currentIdentityCount) {
      return io.to(req.user).emit('updateUserSubscription', {
        status: 'error',
        message: `Cannot downgrade to a plan with ${ subscriptionPackage.maxIdentities } identities. User currently has ${ currentIdentityCount } identities. Please remove identities first or contact admin for override.`,
      })
    }

    // Get the new Paystack plan code based on billing cycle
    const newPaystackPlanCode = billingCycle === 'yearly'
      ? subscriptionPackage.paystackPlanCodes?.yearly
      : subscriptionPackage.paystackPlanCodes?.monthly

    if (!newPaystackPlanCode) {
      return io.to(req.user).emit('updateUserSubscription', {
        status: 'error',
        message: `No Paystack plan code found for ${billingCycle} billing cycle`,
      })
    }

    // Handle Paystack subscription update if user has an active subscription
    let paystackUpdateResult = { success: true, error: null }

    if (userSubscription && userSubscription.paystackSubscriptionCode && userSubscription.status === 'active') {
      paystackUpdateResult = await handlePaystackSubscriptionUpdate(
        userSubscription.paystackSubscriptionCode,
        newPaystackPlanCode,
        targetUser.email,
      )

      if (paystackUpdateResult.success) {
        // Update local subscription record with new plan details
        userSubscription.planCode = newPaystackPlanCode
        userSubscription.currentPackage = subscriptionPlanId

        // Update with new subscription code if provided (replacement method)
        if (paystackUpdateResult.newSubscriptionCode) {
          userSubscription.paystackSubscriptionCode = paystackUpdateResult.newSubscriptionCode
        }

        // Calculate new end date based on billing cycle
        const now = new Date()
        const daysToAdd = billingCycle === 'yearly' ? 365 : 30

        userSubscription.endsAt = new Date(now.getTime() + (daysToAdd * 24 * 60 * 60 * 1000))

        await userSubscription.save()
        console.log('Paystack subscription updated successfully:', paystackUpdateResult.data)
      }
    }

    // If Paystack update failed and this is a paid subscription, don't proceed with local update
    if (!paystackUpdateResult.success && userSubscription && userSubscription.status === 'active') {
      return io.to(req.user).emit('updateUserSubscription', {
        status: 'error',
        message: `Failed to update subscription with payment provider: ${paystackUpdateResult.error}. Please try again or contact support.`,
      })
    }

    // Update local user subscription plan
    targetUser.subscriptionPlan = subscriptionPlanId

    // Update maxIdentities only if not overridden by admin
    if (!targetUser.maxIdentitiesOverride) {
      targetUser.maxIdentities = subscriptionPackage.maxIdentities
    }

    await targetUser.save()

    // Update or create user subscription record if it doesn't exist
    if (!userSubscription) {
      const newUserSubscription = new UserSubscription({
        user: req.id,
        planCode: newPaystackPlanCode,
        currentPackage: subscriptionPlanId,
        status: 'active',
        startedAt: new Date(),
        endsAt: new Date(Date.now() + (billingCycle === 'yearly' ? 365 : 30) * 24 * 60 * 60 * 1000),
      })

      await newUserSubscription.save()
    }

    // Get updated user data
    const updatedUser = await User.findById(req.id)
      .populate('subscriptionPlan', 'title maxIdentities priceMonthly priceYearly')
      .select('-password -resetPasswordToken -resetPasswordExpire')

    const responseData = {
      user: updatedUser,
      previousSubscription,
      newSubscription: subscriptionPackage,
      maxIdentitiesChanged: !targetUser.maxIdentitiesOverride,
      previousMaxIdentities,
      newMaxIdentities: targetUser.maxIdentities,
      remainingSlots: targetUser.getRemainingIdentitySlots(),
      paystackUpdated: paystackUpdateResult.success,
      paystackMethod: paystackUpdateResult.method,
      paystackUpdateLink: paystackUpdateResult.updateLink,
      requiresUserAction: paystackUpdateResult.requiresUserAction,
      billingCycle,
    }

    // Customize message based on Paystack update method
    let successMessage = `Subscription updated to ${ subscriptionPackage.title }${ !targetUser.maxIdentitiesOverride ? ` with ${ targetUser.maxIdentities } max identities` : '' }`

    if (paystackUpdateResult.requiresUserAction && paystackUpdateResult.updateLink) {
      successMessage += '. Please complete the payment update using the provided link.'
    } else if (paystackUpdateResult.method === 'replacement') {
      successMessage += '. Payment subscription has been updated automatically.'
    }

    io.to(req.user).emit('updateUserSubscription', {
      status: 'success',
      message: successMessage,
      data: responseData,
    })

    // Notify the target user if they're online and it's not their own update
    if (!isOwnData) {
      io.to(req.id).emit('subscriptionUpdated', {
        status: 'success',
        message: `Your subscription has been updated to ${ subscriptionPackage.title }`,
        data: {
          newSubscription: subscriptionPackage,
          maxIdentities: targetUser.maxIdentities,
          maxIdentitiesOverride: targetUser.maxIdentitiesOverride,
          remainingSlots: targetUser.getRemainingIdentitySlots(),
        },
      })
    }
  } catch (error) {
    console.error('Error updating user subscription:', error)
    io.to(req.user).emit('updateUserSubscription', {
      status: 'error',
      message: error.message,
    })
  }
})

// @desc    Cancel user subscription
// @route   Socket: userSubscriptionController:cancelUserSubscription
// @access  Private (Self or Admin)
exports.cancelUserSubscription = asyncHandler(async (req, res, next) => {
  try {
    console.log('cancelUserSubscription', { req })

    // Check if current user is admin or canceling their own subscription
    const currentUser = await User.findById(req.user)
    const isAdmin = currentUser && currentUser.role === 'admin'
    const isOwnData = req.user.toString() === req.id.toString()

    if (!isAdmin && !isOwnData) {
      return io.to(req.user).emit('cancelUserSubscription', {
        status: 'error',
        message: 'Access denied. You can only cancel your own subscription or be an admin.',
      })
    }

    // Find the target user
    const targetUser = await User.findById(req.id)
    if (!targetUser) {
      return io.to(req.user).emit('cancelUserSubscription', {
        status: 'error',
        message: 'User not found',
      })
    }

    // Find user subscription
    const userSubscription = await UserSubscription.findOne({ user: req.id })

    if (!userSubscription) {
      return io.to(req.user).emit('cancelUserSubscription', {
        status: 'error',
        message: 'No active subscription found',
      })
    }

    // If subscription is already expired or cancelled, don't proceed
    if (userSubscription.status === 'expired') {
      return io.to(req.user).emit('cancelUserSubscription', {
        status: 'error',
        message: 'Subscription is already expired',
      })
    }

    // Cancel subscription with Paystack if it has a subscription code
    let paystackCancellationResult = { success: true, error: null }

    if (userSubscription.paystackSubscriptionCode) {
      paystackCancellationResult = await handlePaystackSubscriptionCancellation(
        userSubscription.paystackSubscriptionCode,
      )

      if (paystackCancellationResult.success) {
        console.log('Paystack subscription cancelled successfully:', paystackCancellationResult.data)
      } else {
        // For cancellation, we might want to continue with local cancellation even if Paystack fails
        // This ensures users can still cancel their subscription locally
        console.log('Continuing with local cancellation despite Paystack error:', paystackCancellationResult.error)
      }
    }

    // Update subscription status to expired
    userSubscription.status = 'expired'
    userSubscription.endsAt = new Date() // Set end date to now
    await userSubscription.save()

    // Optionally downgrade user to basic plan or remove subscription plan
    // You might want to keep them on a free plan instead of removing completely
    const basicPlan = await SubscriptionPackage.findOne({ title: 'Basic' })
    if (basicPlan) {
      targetUser.subscriptionPlan = basicPlan._id

      // Only update maxIdentities if not overridden by admin
      if (!targetUser.maxIdentitiesOverride) {
        targetUser.maxIdentities = basicPlan.maxIdentities
      }
    } else {
      // If no basic plan, remove subscription plan
      targetUser.subscriptionPlan = null
      if (!targetUser.maxIdentitiesOverride) {
        targetUser.maxIdentities = 1
      }
    }

    await targetUser.save()

    // Get updated user data
    const updatedUser = await User.findById(req.id)
      .populate('subscriptionPlan', 'title maxIdentities priceMonthly priceYearly')
      .select('-password -resetPasswordToken -resetPasswordExpire')

    const responseMessage = paystackCancellationResult.success
      ? 'Subscription cancelled successfully'
      : `Subscription cancelled locally. Payment provider cancellation ${paystackCancellationResult.error ? 'failed: ' + paystackCancellationResult.error : 'may need manual verification'}`

    io.to(req.user).emit('cancelUserSubscription', {
      status: 'success',
      message: responseMessage,
      data: {
        user: updatedUser,
        cancelledSubscription: userSubscription,
        paystackCancelled: paystackCancellationResult.success,
        paystackError: paystackCancellationResult.error,
      },
    })

    // Notify the target user if they're online and it's not their own cancellation
    if (!isOwnData) {
      io.to(req.id).emit('subscriptionCancelled', {
        status: 'success',
        message: 'Your subscription has been cancelled',
        data: {
          user: updatedUser,
        },
      })
    }

    // Send cancellation confirmation email
    try {
      await sendEmail({
        email: targetUser.email,
        subject: 'Subscription Cancelled - QwoteZ',
        message: `
          <h2>Subscription Cancelled</h2>
          <p>Your subscription has been successfully cancelled.</p>
          <p>You will continue to have access to your current plan until ${userSubscription.endsAt ? userSubscription.endsAt.toDateString() : 'the end of your billing period'}.</p>
          <p>If you have any questions, please contact our support team.</p>
        `,
      })
    } catch (emailError) {
      console.error('Error sending cancellation email:', emailError)

      // Don't fail the cancellation if email fails
    }

  } catch (error) {
    console.error('Error canceling user subscription:', error)
    io.to(req.user).emit('cancelUserSubscription', {
      status: 'error',
      message: error.message,
    })
  }
})

// @desc    Handle Paystack subscription webhooks
// @route   POST /api/webhooks/paystack/subscription
// @access  Public (Paystack webhook)
exports.handlePaystackSubscriptionWebhook = asyncHandler(async (req, res, next) => {
  try {
    const { event, data } = req.body

    console.log('Paystack subscription webhook received:', { event, data })

    // Verify webhook signature (recommended for production)
    // const signature = req.headers['x-paystack-signature']
    // if (!verifyPaystackSignature(req.body, signature)) {
    //   return res.status(400).json({ error: 'Invalid signature' })
    // }

    switch (event) {
    case 'subscription.create':
      await handleSubscriptionCreated(data)
      break

    case 'subscription.disable':
      await handleSubscriptionDisabled(data)
      break

    case 'subscription.not_renewing':
      await handleSubscriptionNotRenewing(data)
      break

    case 'invoice.create':
      await handleInvoiceCreated(data)
      break

    case 'invoice.payment_failed':
      await handleInvoicePaymentFailed(data)
      break

    default:
      console.log('Unhandled webhook event:', event)
    }

    res.status(200).json({ status: 'success' })

  } catch (error) {
    console.error('Error handling Paystack webhook:', error)
    res.status(500).json({ error: 'Webhook processing failed' })
  }
})

// Helper functions for webhook handling
const handleSubscriptionCreated = async data => {
  try {
    const { customer, plan, subscription_code } = data

    const user = await User.findOne({ email: customer.email })
    if (!user) {
      console.error('User not found for subscription creation:', customer.email)

      return
    }

    const userSubscription = await UserSubscription.findOne({ user: user._id })
    if (userSubscription) {
      userSubscription.paystackSubscriptionCode = subscription_code
      userSubscription.status = 'active'
      await userSubscription.save()
      console.log('Updated subscription with Paystack subscription code:', subscription_code)
    }
  } catch (error) {
    console.error('Error handling subscription created webhook:', error)
  }
}

const handleSubscriptionDisabled = async data => {
  try {
    const { subscription_code } = data

    const userSubscription = await UserSubscription.findOne({
      paystackSubscriptionCode: subscription_code,
    })

    if (userSubscription) {
      userSubscription.status = 'expired'
      userSubscription.endsAt = new Date()
      await userSubscription.save()
      console.log('Subscription disabled via webhook:', subscription_code)
    }
  } catch (error) {
    console.error('Error handling subscription disabled webhook:', error)
  }
}

const handleSubscriptionNotRenewing = async data => {
  try {
    const { subscription_code } = data

    const userSubscription = await UserSubscription.findOne({
      paystackSubscriptionCode: subscription_code,
    })

    if (userSubscription) {
      // Mark subscription as not renewing but keep it active until end date
      console.log('Subscription not renewing:', subscription_code)

      // You might want to add a field to track this state
    }
  } catch (error) {
    console.error('Error handling subscription not renewing webhook:', error)
  }
}

const handleInvoiceCreated = async data => {
  try {
    const { customer, subscription } = data

    console.log('Invoice created for subscription:', subscription?.subscription_code)

    // Handle invoice creation logic here
  } catch (error) {
    console.error('Error handling invoice created webhook:', error)
  }
}

const handleInvoicePaymentFailed = async data => {
  try {
    const { customer, subscription } = data

    console.log('Invoice payment failed for subscription:', subscription?.subscription_code)

    // You might want to notify the user or take action on failed payments
    const user = await User.findOne({ email: customer.email })
    if (user) {
      // Send payment failure notification
      await sendEmail({
        email: user.email,
        subject: 'Payment Failed - QwoteZ',
        message: `
          <h2>Payment Failed</h2>
          <p>We were unable to process your subscription payment.</p>
          <p>Please update your payment method to continue using our services.</p>
        `,
      })
    }
  } catch (error) {
    console.error('Error handling invoice payment failed webhook:', error)
  }
}